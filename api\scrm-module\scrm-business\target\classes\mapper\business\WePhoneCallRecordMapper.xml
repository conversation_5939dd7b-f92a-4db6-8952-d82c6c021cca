<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WePhoneCallRecordMapper">

    <resultMap type="org.scrm.domain.phone.WePhoneCallRecord" id="WePhoneCallRecordResult">
        <id property="id" column="id"/>
        <result property="sopBaseId" column="sop_base_id"/>
        <result property="executeTargetId" column="execute_target_id"/>
        <result property="externalUserid" column="external_userid"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerPhone" column="customer_phone"/>
        <result property="weUserId" column="we_user_id"/>
        <result property="callMethod" column="call_method"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 查询指定员工的拨打电话记录 -->
    <select id="findCallRecordsByWeUserId" resultMap="WePhoneCallRecordResult">
        SELECT * FROM we_phone_call_record
        WHERE del_flag = 0
        AND we_user_id = #{weUserId}
        <if test="sopBaseId != null and sopBaseId != ''">
            AND sop_base_id = #{sopBaseId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询指定客户的拨打电话记录 -->
    <select id="findCallRecordsByCustomer" resultMap="WePhoneCallRecordResult">
        SELECT * FROM we_phone_call_record
        WHERE del_flag = 0
        AND external_userid = #{externalUserid}
        AND we_user_id = #{weUserId}
        ORDER BY create_time DESC
    </select>

    <!-- 查询指定SOP执行目标的拨打记录 -->
    <select id="findCallRecordsByExecuteTarget" resultMap="WePhoneCallRecordResult">
        SELECT * FROM we_phone_call_record
        WHERE del_flag = 0
        AND execute_target_id = #{executeTargetId}
        ORDER BY create_time DESC
    </select>

    <!-- 检查客户是否已被拨打过电话 -->
    <select id="hasCalledCustomer" resultType="boolean">
        SELECT COUNT(1) > 0 FROM we_phone_call_record
        WHERE del_flag = 0
        AND external_userid = #{externalUserid}
        AND we_user_id = #{weUserId}
        <if test="sopBaseId != null and sopBaseId != ''">
            AND sop_base_id = #{sopBaseId}
        </if>
    </select>

    <!-- 统计员工在指定SOP下的拨打电话数量 -->
    <select id="countCallRecordsBySop" resultType="int">
        SELECT COUNT(DISTINCT external_userid) FROM we_phone_call_record
        WHERE del_flag = 0
        AND we_user_id = #{weUserId}
        <if test="sopBaseId != null and sopBaseId != ''">
            AND sop_base_id = #{sopBaseId}
        </if>
    </select>

</mapper>
