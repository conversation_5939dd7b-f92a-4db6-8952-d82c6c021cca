{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753152660518}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}